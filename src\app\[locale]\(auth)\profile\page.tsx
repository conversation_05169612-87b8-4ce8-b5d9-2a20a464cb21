import OrdersList from "@/app/[locale]/(order)/OrdersList";
import UserProfileCard from "@/components/UserProfileCard";
import { Loader2 } from "lucide-react";
import { Suspense } from "react";

const ProfilePage = async () => {
  return (
    <main className="container-full flex min-h-[80vh] flex-col">
      <section className="mx-auto grid w-full max-w-xl gap-8">
        <UserProfileCard />
        <Suspense
          fallback={
            <div className="mx-auto flex h-96 items-center justify-center">
              <Loader2 className="size-6 animate-spin" />
            </div>
          }
        >
          <OrdersList />
        </Suspense>
      </section>
    </main>
  );
};

export default ProfilePage;
