"use client";

import { useEffect } from "react";
import Link from "next/link";
import { useTranslations } from "next-intl";
import { Button, buttonVariants } from "@/components/ui/button";
import { cn } from "@/lib/utils";

type ErrorProps = {
  error: Error & { digest?: string };
  reset: () => void;
};

export default function Error({
  error,
  reset,
}: ErrorProps) {
  const t = useTranslations("Error");

  useEffect(() => {
    console.error(error);
  }, [error]);

  return (
    <div className="bg-background flex min-h-[80vh] flex-col items-center justify-center p-4 text-center">
      <div className="space-y-6">
        <div className="space-y-2">
          <h1 className="text-white text-4xl font-bold">
            {t("title")}
          </h1>
          <p className="text-foreground font-semibold">{t("description")}</p>
          <pre className="text-foreground mt-4 overflow-x-auto rounded-lg bg-muted/50 p-4 text-left text-sm">
            {error.message}
          </pre>
        </div>
        <div className="flex flex-col items-center justify-center gap-4 sm:flex-row">
          <Button
            variant="secondary"
            onClick={() => reset()}
            className={cn(
              "w-full sm:w-auto bg-primary-foreground/50"
            )}
          >
            {t("tryAgain")}
          </Button>
          <Link
            href="/"
            className={cn(
              buttonVariants({ variant: "default" }),
              "w-full sm:w-auto"
            )}
          >
            {t("backToHome")}
          </Link>
        </div>
      </div>
    </div>
  );
}
