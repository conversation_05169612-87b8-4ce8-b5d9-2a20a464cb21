"use client";

import { But<PERSON> } from "@/components/ui/button";
import { cn } from "@/lib/utils";
import { LogOut } from "lucide-react";
import { useTranslations } from "next-intl";
import { authClient } from "@/lib/auth/client";
import { useTransition } from "react";

type ProfileLogoutButtonProps = {
  className?: string;
};

export default function ProfileLogoutButton({
  className,
}: ProfileLogoutButtonProps) {
  const t = useTranslations("Profile");
  const [signOutPending, startSignOutTransition] = useTransition();

  const handleSignOut = () => {
    startSignOutTransition(async () => {
      await authClient.signOut({
        fetchOptions: {
          onSuccess: () => window.location.reload(),
        },
      });
    });
  };

  return (
    <Button
      variant="outline"
      size="sm"
      onClick={handleSignOut}
      disabled={signOutPending}
      className={cn("border-destructive text-destructive w-fit", className)}
    >
      <LogOut className="size-4" />
      {signOutPending ? t("signingOut") : t("logout")}
    </Button>
  );
}
