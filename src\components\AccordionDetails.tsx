"use client";

import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";
import { cn } from "@/lib/utils";
import Text, { textVariants } from "@/components/Text";
import { useState } from "react";

interface AccordionDetailsProps {
  details: string[][];
  detailsTitle: string;
  careTitle: string;
  className?: string;
}

export const AccordionDetails = ({
  details,
  detailsTitle,
  careTitle,
  className,
}: AccordionDetailsProps) => {
  const [value, setValue] = useState(["item-1", "item-2"]);

  return (
    <div className={cn(className)}>
      <Accordion
        type="multiple"
        value={value}
        onValueChange={(value) => setValue(value)}
      >
        {details.map((detail, index) => (
          <AccordionItem
            key={index}
            value={`item-${index + 1}`}
            className="group"
          >
            <AccordionTrigger className="rounded-t-none border-t group-first-of-type:border-t-2">
              {index === 0 ? detailsTitle : careTitle}
            </AccordionTrigger>
            <AccordionContent className="">
              <ul className="list-inside list-disc">
                {detail.map((item, index2) => (
                  <li
                    className={cn(
                      textVariants({ size: "sm" }),
                      index === 0 &&
                        "lowercase last-of-type:mt-4 last-of-type:list-none",
                      "font-medium",
                    )}
                    key={index2}
                  >
                    {item}
                  </li>
                ))}
              </ul>
            </AccordionContent>
          </AccordionItem>
        ))}
      </Accordion>
    </div>
  );
};
