import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { cn } from "@/lib/utils";
import { Package } from "lucide-react";
import Text from "@/components/Text";
import { getTranslations } from "next-intl/server";
import { getUserOrdersById } from "@/app/[locale]/(order)/actions";
import OrderCard from "@/app/[locale]/(order)/OrderCard";

type OrdersListProps = {
  className?: string;
};

export default async function OrdersList({ className }: OrdersListProps) {
  const orders = await getUserOrdersById();
  const t = await getTranslations("Orders");

  if (orders.length === 0) {
    return (
      <Card className={cn("w-full", className)}>
        <CardContent className="flex flex-col items-center justify-center py-12">
          <Package className="text-muted-foreground mb-4 h-12 w-12" />
          <Text as="h3" size="lg" className="mb-2 font-semibold">
            {t("noOrders")}
          </Text>
          <Text as="p" size="sm" className="text-muted-foreground text-center">
            {t("noOrdersDescription")}
          </Text>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className={cn("w-full", className)}>
      <CardHeader>
        <CardTitle className="text-xl">{t("yourOrders")}</CardTitle>
      </CardHeader>
      <CardContent className="flex flex-col gap-8">
        {orders.map((order) => (
          <OrderCard key={order.id} order={order} />
        ))}
      </CardContent>
    </Card>
  );
}
