import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>it<PERSON> } from "@/components/ui/card";
import { cn } from "@/lib/utils";
import { Calendar, MapPin, CreditCard } from "lucide-react";
import Text from "@/components/Text";
import { Badge } from "@/components/ui/badge";
import { formatKwan<PERSON> } from "@/lib/currency";
import { OrderType } from "@/app/[locale]/(order)/model";
import { Separator } from "@/components/ui/separator";
import { getTranslations } from "next-intl/server";
import { ReactNode } from "react";
import OrderUserActions from "./OrderUserActions";

const statusColors = {
  PENDING: "bg-yellow-100 text-yellow-800",
  COMPLETED: "bg-green-100 text-green-800",
  CANCELLED: "bg-red-100 text-red-800",
} satisfies Record<OrderType["status"], string>;

type OrderCardProps = {
  order: OrderType;
  children?: ReactNode;
};

export default async function OrderCard({ order }: OrderCardProps) {
  const t = await getTranslations("Orders");
  const tPaymentMethods = await getTranslations("CheckoutForm.paymentMethods");

  return (
    <Card className="relative gap-2">
      <CardHeader>
        <div className="flex flex-wrap items-center justify-between gap-2">
          <div className="min-w-0 flex-1">
            <CardTitle className="text-base text-wrap">
              {t("orderNumber")}: {order.id}
            </CardTitle>
          </div>
          <div className="flex items-center gap-2">
            <Badge
              className={cn(
                "text-xs font-semibold",
                statusColors[order.status],
              )}
            >
              {t(`status.${order.status}`)}
            </Badge>
            <OrderUserActions order={order} />
          </div>
        </div>
      </CardHeader>

      <CardContent className="flex flex-col gap-4">
        <article className="flex flex-wrap gap-4 text-sm">
          <div className="flex items-center gap-2">
            <Calendar className="text-muted-foreground size-4" />
            <span>{new Date(order.createdAt).toLocaleDateString()}</span>
          </div>
          <div className="flex items-center gap-2">
            <MapPin className="text-muted-foreground size-4" />
            <span className="truncate">{order.storeName}</span>
          </div>
          <div className="flex items-center gap-2">
            <CreditCard className="text-muted-foreground size-4" />
            <span className="truncate">
              {tPaymentMethods(order.paymentMethod)}
            </span>
          </div>
        </article>
        <Separator />
        <article className="flex items-baseline justify-between">
          <Text as="span" size="sm" className="text-muted-foreground">
            {t("total")}:
          </Text>
          <Text as="span" size="lg" className="font-semibold">
            {formatKwanza(order.total)}
          </Text>
        </article>
      </CardContent>
    </Card>
  );
}
