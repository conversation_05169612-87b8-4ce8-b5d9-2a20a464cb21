"use client";
import { useTranslations } from "next-intl";
import { Separator } from "@/components/ui/separator";
import Text from "@/components/Text";
import { formatKwanza } from "@/lib/currency";
import { cn } from "@/lib/utils";

type CartCheckoutProps = {
  subtotal: number;
  total: number;
  children: React.ReactNode;
  className?: string;
};

const CartCheckout = ({
  subtotal,
  total,
  children,
  className,
}: CartCheckoutProps) => {
  const t = useTranslations("Cart");

  return (
    <section className={cn("flex flex-col gap-4", className)}>
      <Text as="h6" size="lg" className="font-semibold">
        {t("summaryTitle")}
      </Text>
      <Separator />
      <dl className="flex flex-col gap-4">
        <Text as="span" size="sm" className="flex items-center justify-between">
          <dt className="text-sm">{t("subtotal")}</dt>
          <dd className="text-sm font-medium">{formatKwanza(subtotal)}</dd>
        </Text>
        <Separator />
        <Text
          as="span"
          size="sm"
          className="flex items-center justify-between text-base font-medium"
        >
          <dt>{t("total")}</dt>
          <dd>{formatKwanza(total)}</dd>
        </Text>
      </dl>

      {children}
    </section>
  );
};

export default CartCheckout;
