"use client";

import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { useTranslations } from "next-intl";
import { useTransition } from "react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Loader2 } from "lucide-react";
import { cn } from "@/lib/utils";
import { authClient } from "@/lib/auth/client";
import { ProfileFormType, profileSchema } from "./model";
import { useRouter } from "@/i18n/navigation";
import { toast } from "sonner";
import { linksObj } from "@/app/links";

interface ProfileFormProps {
  userData?: ProfileFormType;
}

const ProfileForm = ({ userData }: ProfileFormProps) => {
  const t = useTranslations("Auth");
  const { replace, push } = useRouter();
  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useForm<ProfileFormType>({
    resolver: zodResolver(profileSchema),
    defaultValues: userData,
  });
  const [isPending, startTransition] = useTransition();
  const [signOutPending, startSignOutTransition] = useTransition();

  const handleUpdateProfile = (data: ProfileFormType) => {
    startTransition(async () => {
      const { data: result, error } = await authClient.updateUser({
        ...data,
        name: `${data.firstName} ${data.lastName}`,
      });

      if (error) {
        console.error("Update user error:", error);
        toast.error(t("profileUpdateFailed"));
        return;
      }
      if (result) {
        toast.success(t("profileUpdateSuccess"));
        push(linksObj.profile.href);
      } else {
        toast.error(t("profileUpdateFailed"));
      }
    });
  };

  const handleSignOut = () => {
    startSignOutTransition(async () => {
      await authClient.signOut({
        fetchOptions: {
          onSuccess: () => replace(linksObj.signOn.href),
        },
      });
    });
  };

  const fields = [
    {
      name: "firstName",
      label: t("firstName"),
      type: "text",
      placeholder: "Ex: João",
      transform: (value: string) => value.replace(/\s/g, ""),
    },
    {
      name: "lastName",
      label: t("lastName"),
      type: "text",
      placeholder: "Ex: Silva",
      transform: (value: string) => value.replace(/\s/g, ""),
    },
    {
      name: "phoneNumber",
      label: t("phoneNumber"),
      type: "tel",
      placeholder: "Ex: +244912912913",
      transform: undefined,
    },
  ] as const;

  return (
    <form onSubmit={handleSubmit(handleUpdateProfile)}>
      <div className="grid gap-6">
        {fields.map((field) => (
          <div key={field.name} className="grid gap-2">
            <Label htmlFor={field.name}>{field.label}</Label>
            <Input
              id={field.name}
              type={field.type}
              placeholder={field.placeholder}
              {...register(field.name, {
                onChange: (e) => {
                  e.target.value = field.transform
                    ? field.transform(e.target.value)
                    : e.target.value;
                },
              })}
              aria-invalid={!!errors[field.name]}
            />
            {errors[field.name] && (
              <p className="text-destructive text-sm">
                {errors[field.name]?.message}
              </p>
            )}
          </div>
        ))}
        <Button
          type="submit"
          size="sm"
          className="w-full"
          disabled={isPending || Object.keys(errors).length > 0}
        >
          {t("updateProfile")}
          {isPending && <Loader2 className="size-4 animate-spin p-0.5" />}
        </Button>
        <Button
          type="button"
          variant="link"
          size="sm"
          className={cn(
            "hocus:text-destructive mx-auto w-fit",
            signOutPending && "text-destructive animate-pulse",
          )}
          onClick={handleSignOut}
          disabled={signOutPending || isPending}
        >
          {t("signOut")}
        </Button>
      </div>
    </form>
  );
};

export default ProfileForm;
