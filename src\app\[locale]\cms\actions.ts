"use server";

import {
  orderCancelledEmailTemplate,
  orderCompletedEmailTemplate,
  sendEmail,
} from "@/lib/mailer";
import prisma from "@/lib/prisma";
import { OrderStatus } from "@/generated/prisma";
import { revalidateTag } from "next/cache";
import { z } from "zod";
import { getOrderById, ORDER_CACHE_KEY } from "../(order)/actions";
import { requireAdminUser } from "../(auth)/user-dal";

const updateOrderStatusSchema = z.object({
  orderId: z.string(),
  newStatus: z.nativeEnum(OrderStatus),
});

export const updateOrderStatus = async (data: {
  orderId: string;
  newStatus: OrderStatus;
}): Promise<{ success: boolean; data: string }> => {
  await requireAdminUser();

  try {
    const validatedData = updateOrderStatusSchema.parse(data);

    const order = await getOrderById(validatedData.orderId);

    if (!order) {
      return {
        success: false,
        data: "Pedido não encontrado.",
      };
    }

    const updatedOrder = await prisma.order.update({
      where: { id: validatedData.orderId },
      data: { status: validatedData.newStatus },
    });

    const customerName = `${order.firstName} ${order.lastName}`;
    const emailItems = order.items.map((item) => ({
      name: item.name,
      size: item.size.euSize,
      quantity: item.quantity,
      price: item.price,
    }));

    const emailData = {
      [OrderStatus.COMPLETED]: {
        subject: `Comprovativo de Compra - ${order.id}`,
        html: orderCompletedEmailTemplate({
          orderReference: order.id,
          customerName,
          items: emailItems,
          storeName: order.storeName,
          storeAddress: order.storeAddress,
        }),
      },
      [OrderStatus.CANCELLED]: {
        subject: `Pedido Cancelado - ${order.id}`,
        html: orderCancelledEmailTemplate({
          orderReference: order.id,
          customerName,
          items: emailItems,
          total: order.total,
        }),
      },
      [OrderStatus.PENDING]: null,
    }[validatedData.newStatus];

    if (emailData) {
      await sendEmail({
        to: order.email,
        cc: process.env.SALES_EMAIL || "<EMAIL>",
        subject: emailData.subject,
        html: emailData.html,
      });
    }
    revalidateTag(ORDER_CACHE_KEY);
    return { success: true, data: updatedOrder.id };
  } catch (error) {
    console.error("Error updating order status:", error);

    if (error instanceof z.ZodError) {
      return {
        success: false,
        data: "Os dados fornecidos não são válidos.",
      };
    }

    return {
      success: false,
      data: "Erro interno do servidor. Tente novamente.",
    };
  }
};
