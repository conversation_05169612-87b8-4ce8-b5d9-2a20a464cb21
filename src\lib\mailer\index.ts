import { z } from "zod";
import nodemailer from "nodemailer";
import { paymentMethods } from "@/app/[locale]/(cart)/model";
import {
  PAYMENT_COORDINATES,
  CONTACT_INFO,
} from "@/app/[locale]/(cart)/paymentConstants";
import { formatKwanza } from "../currency";

const envSchema = z.object({
  SMTP_HOST: z.string().min(1, "SMTP_HOST is required"),
  SMTP_PORT: z.coerce.number().min(1, "SMTP_PORT is required"),
  SMTP_USER: z.string().min(1, "SMTP_USER is required"),
  SMTP_PASS: z.string().min(1, "SMTP_PASS is required"),
  SMTP_FROM: z.string().optional(),
});

const env = envSchema.parse(process.env);

const transporter = nodemailer.createTransport({
  host: env.SMTP_HOST,
  port: env.SMTP_PORT,
  secure: process.env.NODE_ENV === "production",
  auth: {
    user: env.SMTP_USER,
    pass: env.SMTP_PASS,
  },
});

export async function sendEmail({
  to,
  cc,
  subject,
  html,
}: {
  to: string;
  cc?: string;
  subject: string;
  html: string;
}) {
  const info = await transporter.sendMail({
    from: env.SMTP_FROM || env.SMTP_USER,
    to,
    cc,
    subject,
    html,
  });

  if (process.env.NODE_ENV !== "production") {
    const previewUrl = nodemailer.getTestMessageUrl?.(info);
    if (previewUrl) console.log(`Preview URL: ${previewUrl}`);
  }
}

export function otpEmailTemplate({
  otp,
  expiresIn,
}: {
  otp: string;
  expiresIn: number;
}) {
  return `
    <div style="font-family: Arial, sans-serif;">
      <h2>Your Verification Code</h2>
      <p>Your one-time password (OTP) is:</p>
      <div style="font-size: 2em; font-weight: bold; margin: 16px 0;">${otp}</div>
      <p>This code will expire in ${Math.floor(expiresIn / 60)} minutes.</p>
      <p>If you did not request this, please ignore this email.</p>
    </div>
  `;
}

const LAUNCH_DATE = new Date("2025-07-03T00:00:00.000Z");

function isBeforeLaunchDate(): boolean {
  return new Date() < LAUNCH_DATE;
}

function getPaymentCoordinates(): string {
  return `
    <div style="background: #fff; padding: 15px; border-radius: 5px; margin: 10px 0;">
      <div style="margin-bottom: 10px;">
        <strong>Entidade:</strong> ${PAYMENT_COORDINATES.entity}
      </div>
      <div style="margin-bottom: 10px;">
        <strong>NIF:</strong> ${PAYMENT_COORDINATES.nif}
      </div>
      <div style="margin-bottom: 10px;">
        <strong>Banco:</strong> ${PAYMENT_COORDINATES.bank}
      </div>
      <div style="margin-bottom: 10px;">
        <strong>Conta:</strong> ${PAYMENT_COORDINATES.account}
      </div>
      <div style="margin-bottom: 10px;">
        <strong>IBAN:</strong> ${PAYMENT_COORDINATES.iban}
      </div>
    </div>
  `;
}

function getContactInfo(): string {
  return `
    <div style="margin-top: 20px;">
      <strong>Kurguen Sales</strong><br><br>
      <a href="tel:${CONTACT_INFO.phone}" style="">${CONTACT_INFO.phone}</a> |
      <a href="mailto:${CONTACT_INFO.email}" style="">${CONTACT_INFO.email}</a> |
      <a href="${CONTACT_INFO.website}" style="">${CONTACT_INFO.website}</a>
    </div>
  `;
}

export function orderConfirmationEmailTemplate({
  orderReference,
  customerName,
  items,
  total,
  paymentMethod,
  storeName,
  storeAddress,
}: {
  orderReference: string;
  customerName: string;
  items: Array<{
    name: string;
    size: string;
    quantity: number;
    price: number;
  }>;
  total: number;
  paymentMethod: keyof typeof paymentMethods;
  storeName: string;
  storeAddress: string;
}) {
  if (paymentMethod === paymentMethods.PAY_IN_STORE) {
    return generatePayInStoreTemplate({
      orderReference,
      customerName,
      items,
      total,
      storeName,
      storeAddress,
    });
  }

  if (isBeforeLaunchDate()) {
    return generatePreLaunchTemplate({
      customerName,
      items,
      total,
    });
  }

  return generatePostLaunchTemplate({
    customerName,
    items,
    total,
  });
}

function generatePreLaunchTemplate({
  customerName,
  items,
  total,
}: {
  customerName: string;
  items: Array<{
    name: string;
    size: string;
    quantity: number;
    price: number;
  }>;
  total: number;
}) {
  const itemsInfo = items
    .map((item) => `${item.name}, <strong>tamanho</strong> ${item.size}`)
    .join(", ");

  return `
    <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
      <h2 style="color: #333;">Pré-Encomenda</h2>
      <p>Olá ${customerName},</p>
      <p>A sua experiência Kurguen já começou! Solicitou a pré-encomenda dos sneakers Kurguen-Luanda, ${itemsInfo}, no <strong>valor</strong> de ${formatKwanza(total)}.</p>

      <div style="background: #f9f9f9; padding: 20px; margin: 20px 0; border-radius: 8px;">
        <p><strong>1.</strong> Para concluir a compra, efetue o pagamento para as seguintes coordenadas:</p>
        ${getPaymentCoordinates()}

        <p><strong>2.</strong> Envie o comprovativo para: <EMAIL> ou ${CONTACT_INFO.phone}</p>

        <p><strong>3.</strong> Após confirmação, receberá o recibo e a referência de compra</p>

        <p><strong>4.</strong> O levantamento será feito no dia 3 de julho, na loja Rose Palhares – Ilha de Luanda.</p>
      </div>

      ${getContactInfo()}
    </div>
  `;
}

function generatePostLaunchTemplate({
  customerName,
  items,
  total,
}: {
  customerName: string;
  items: Array<{
    name: string;
    size: string;
    quantity: number;
    price: number;
  }>;
  total: number;
}) {
  const itemsInfo = items
    .map((item) => `${item.name}, <strong>tamanho</strong> ${item.size}`)
    .join(", ");

  return `
    <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
      <p>Olá ${customerName},</p>
      <p>A sua experiência Kurguen já começou! Solicitou os sneakers Kurguen-Luanda, ${itemsInfo}, no <strong>valor</strong> de ${formatKwanza(total)}.</p>

      <div style="background: #f9f9f9; padding: 20px; margin: 20px 0; border-radius: 8px;">
        <p><strong>1.</strong> Para concluir a compra, efetue o pagamento para as seguintes coordenadas:</p>
        ${getPaymentCoordinates()}

        <p><strong>2.</strong> Envie o comprovativo para: <EMAIL> ou ${CONTACT_INFO.phone}</p>

        <p><strong>3.</strong> Após confirmação, receberá o recibo e a referência final de compra.</p>
      </div>

      ${getContactInfo()}
    </div>
  `;
}

function generatePayInStoreTemplate({
  orderReference,
  customerName,
  items,
  total,
  storeName,
  storeAddress,
}: {
  orderReference: string;
  customerName: string;
  items: Array<{
    name: string;
    size: string;
    quantity: number;
    price: number;
  }>;
  total: number;
  storeName: string;
  storeAddress: string;
}) {
  const itemsInfo = items
    .map((item) => `${item.name}, <strong>tamanho</strong> ${item.size}`)
    .join(", ");

  return `
    <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
      <p>Olá ${customerName},</p>
      <p>A sua experiência Kurguen já começou! Solicitou a reserva dos sneakers Kurguen-Luanda, ${itemsInfo}, no <strong>valor</strong> de ${formatKwanza(total)} Kz.</p>

      <div style="background: #f9f9f9; padding: 20px; margin: 20px 0; border-radius: 8px;">
        <p><strong>1.</strong> Dirija-se à loja ${storeName}</p>
        <p style="margin-left: 20px;">${storeAddress}</p>

        <p><strong>2.</strong> Mencione a referência: ${orderReference}</p>

        <p><strong>3.</strong> Efetue o pagamento e levante o seu par</p>

        <p><strong>4.</strong> A reserva é válida por 24 horas.</p>
      </div>

      <p>Obrigado,</p>
      ${getContactInfo()}
    </div>
  `;
}

export function orderCompletedEmailTemplate({
  orderReference,
  customerName,
  items,
  // storeName,
  // storeAddress,
}: {
  orderReference: string;
  customerName: string;
  items: Array<{
    name: string;
    size: string;
    quantity: number;
    price: number;
  }>;
  storeName: string;
  storeAddress: string;
}) {
  const itemsInfo = items
    .map((item) => `${item.name}, <strong>tamanho</strong> ${item.size}`)
    .join(", ");

  // <p>O seu pagamento foi confirmado. Os sneakers Kurguen-Luanda, ${itemsInfo}, estão disponíveis para levantamento na loja ${storeName} – ${storeAddress}.</p>

  return `
    <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
      <h2 style="color: #333;">Comprovativo de Compra</h2>
      <p>Obrigado, ${customerName}!</p>
      <p>O seu pagamento foi confirmado. Os sneakers Kurguen-Luanda, ${itemsInfo}, estão disponíveis para levantamento no dia 3 de julho na loja Rose Palhares - Ilha de Luanda.</p>

      <div style="background: #f9f9f9; padding: 20px; margin: 20px 0; border-radius: 8px;">
        <p>No ato de levantamento, mencione a referência: <strong>${orderReference}</strong></p>
      </div>

      <p>Obrigado,</p>
      ${getContactInfo()}
    </div>
  `;
}

export function orderCancelledEmailTemplate({
  orderReference,
  customerName,
  items,
  total,
}: {
  orderReference: string;
  customerName: string;
  items: Array<{
    name: string;
    size: string;
    quantity: number;
    price: number;
  }>;
  total: number;
}) {
  const itemsInfo = items
    .map((item) => `${item.name}, <strong>tamanho</strong> ${item.size}`)
    .join(", ");

  return `
    <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
      <h2 style="color: #333;">Pedido Cancelado</h2>
      <p>Olá ${customerName},</p>
      <p>Lamentamos informar que o seu pedido ${orderReference} foi cancelado.</p>

      <div style="background: #f9f9f9; padding: 20px; margin: 20px 0; border-radius: 8px;">
        <p><strong>Detalhes do pedido cancelado:</strong></p>
        <p>Produtos: ${itemsInfo}</p>
        <p>Valor: ${formatKwanza(total)}</p>
        <p>Referência: ${orderReference}</p>
      </div>

      <p>Se tiver alguma dúvida, entre em contacto connosco.</p>
      <p>Obrigado,</p>
      ${getContactInfo()}
    </div>
  `;
}
