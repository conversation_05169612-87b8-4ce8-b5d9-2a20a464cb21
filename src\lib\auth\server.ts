import { betterAuth } from "better-auth";
import { prismaAdapter } from "better-auth/adapters/prisma";
import prisma from "../prisma";
import { emailOTP } from "better-auth/plugins";
import { sendEmail, otpEmailTemplate } from "../mailer";
import { admin } from "better-auth/plugins";

export const auth = betterAuth({
  user: {
    additionalFields: {
      phoneNumber: {
        type: "string",
        required: false,
        input: true,
      },
    },
  },
  emailAndPassword: {
    enabled: false,
  },
  advanced: {
    cookiePrefix: "_kurguen",
  },
  database: prismaAdapter(prisma, {
    provider: "postgresql",
  }),
  plugins: [
    emailOTP({
      expiresIn: 300, // 5 minutes
      async sendVerificationOTP({ email, otp }) {
        await sendEmail({
          to: email,
          subject: "Kurguen Store - Verification Code",
          html: otpEmailTemplate({ otp, expiresIn: 300 }),
        });
      },
    }),
    admin({}),
  ],
});
