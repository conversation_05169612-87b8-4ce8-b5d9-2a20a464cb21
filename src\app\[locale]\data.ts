import { SizeInfo } from "@/components/SizeSelector";
import <PERSON>car<PERSON><PERSON>ogo from "@/assets/imgs/ducarmo-logo.svg";
import RosePalharesLogo from "@/assets/imgs/rose-palhares-logo.svg";
import VisarLogo from "@/assets/imgs/visar-logo.svg";
import type { ElementType } from "react";

export type ShopInfoType = {
  logo: ElementType;
  name: string;
  address: string;
  phone: string;
  logoClassName?: string;
  disabled?: boolean;
};

export const availableSizes: SizeInfo[] = [
  { euSize: "35", stock: 50 },
  { euSize: "36", stock: 50 },
  { euSize: "37", stock: 50 },
  { euSize: "38", stock: 50 },
  { euSize: "39", stock: 50 },
  { euSize: "40", stock: 50 },
  { euSize: "41", stock: 50 },
  { euSize: "42", stock: 50 },
  { euSize: "43", stock: 50 },
  { euSize: "44", stock: 50 },
  { euSize: "45", stock: 50 },
  { euSize: "46", stock: 50 },
];

export const getShopsData = (translation: {
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  (key: string, ...args: any[]): string;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  <T = string>(key: string, ...args: any[]): T;
}): ShopInfoType[] => [
  {
    logo: DucarmoLogo,
    name: translation("stores.store1.name"),
    address: translation("stores.store1.address"),
    phone: translation("stores.store1.phone"),
    logoClassName: "max-h-10",
    disabled: true,
  },
  {
    logo: RosePalharesLogo,
    name: translation("stores.store2.name"),
    address: translation("stores.store2.address"),
    phone: translation("stores.store2.phone"),
    disabled: true,
  },
  {
    logo: VisarLogo,
    name: translation("stores.store3.name"),
    address: translation("stores.store3.address"),
    phone: translation("stores.store3.phone"),
    disabled: true,
  },
  {
    logo: VisarLogo,
    name: translation("stores.store4.name"),
    address: translation("stores.store4.address"),
    phone: translation("stores.store4.phone"),
    disabled: true,
  },
  {
    logo: VisarLogo,
    name: translation("stores.store5.name"),
    address: translation("stores.store5.address"),
    phone: translation("stores.store5.phone"),
    disabled: false,
  },
];
