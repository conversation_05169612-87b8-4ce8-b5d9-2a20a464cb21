import Image from "next/image";
import aboutCollage from "@/assets/imgs/about-collage.png";
import Text from "@/components/Text";
import { cn } from "@/lib/utils";
import { getTranslations } from "next-intl/server";
import { SlideAnim, RotateScaleAnim } from "../../animations/Animations";

type Props = {
  className?: string;
};

const AboutSection = async ({ className }: Props) => {
  const t = await getTranslations("AboutSection");

  return (
    <section className={cn("flex text-white max-sm:flex-col", className)}>
      <RotateScaleAnim options={{ duration: 1, delay: 0.3 }}>
        <Image
          src={aboutCollage}
          alt="Kurguen brand collage"
          width={500}
          height={500}
          placeholder="blur"
          className="max-h-[80vh] flex-1 object-contain"
        />
      </RotateScaleAnim>
      <SlideAnim
        direction="right"
        stagger
        selector="p,h2"
        options={{ delay: 0.3 }}
      >
        <article id="about" className="flex flex-1 flex-col gap-6">
          <Text as="h2" size="4xl" className="font-bold">
            {t("title")}
          </Text>
          <Text as="p" className="font-semibold">
            {t("subtitle")}
          </Text>
          <Text as="p">{t("paragraph1")}</Text>
          <Text as="p">{t("paragraph2")}</Text>
          <Text as="p">{t("paragraph3")}</Text>
          <Text as="p">{t("paragraph4")}</Text>
        </article>
      </SlideAnim>
    </section>
  );
};

export default AboutSection;
