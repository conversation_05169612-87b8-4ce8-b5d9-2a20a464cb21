import { SlideScaleAnim } from "@/app/animations/Animations";
import Text, { textVariants } from "@/components/Text";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Link } from "@/i18n/navigation";
import { cn } from "@/lib/utils";
import { getTranslations } from "next-intl/server";
import type React from "react";

type AuthCardProps = React.ComponentPropsWithoutRef<"div"> & {
  mode: "profile" | "sign-on";
};

async function AuthCard({
  className,
  mode,
  children,
  ...props
}: AuthCardProps) {
  const t = await getTranslations("Auth");

  return (
    <SlideScaleAnim direction="up" options={{ duration: 0.5 }}>
    <Card
      className={cn("mx-auto flex w-full max-w-md flex-col gap-10", className)}
      {...props}
    >
      <CardHeader className="text-center">
        <CardTitle className="text-2xl">
          {mode === "sign-on" ? t("welcomeBack") : t("updateProfile")}
        </CardTitle>
        <CardDescription>
          {mode === "sign-on" ? t("signInDesc") : t("updateProfileDesc")}
        </CardDescription>
      </CardHeader>
      <CardContent>{children}</CardContent>
      <CardFooter
        className={cn(
          textVariants({ size: "xs" }),
          "[&_a]:text-primary [&_a]:hover:text-primary-foreground flex flex-col gap-5 text-center [&_a]:underline [&_a]:underline-offset-4",
        )}
      >
        <span className="flex flex-col text-center text-balance">
          <Text size="xs">{t("termsText")}</Text>
          <Text as="span" size="xs">
            <Link
              href="#"
              className="text-primary hover:text-primary-foreground"
            >
              {t("termsLink")}
            </Link>{" "}
            {t("andText")}{" "}
            <Link
              href="#"
              className="text-primary hover:text-primary-foreground"
            >
              {t("privacyLink")}
            </Link>
            .
          </Text>
        </span>
      </CardFooter>
    </Card>
    </SlideScaleAnim>
  );
}

export default AuthCard;
