import { Phone, Mail, Instagram, Globe } from "lucide-react";
import { getTranslations } from "next-intl/server";

import KurguenIcon from "@/assets/imgs/kurguen-icon.svg";
import Text, { textVariants } from "@/components/Text";
import { Input } from "@/components/ui/input";
import { Button, buttonVariants } from "@/components/ui/button";
import { cn } from "@/lib/utils";
import { Link } from "@/i18n/navigation";
import { SlideAnim } from "@/app/animations/Animations";

type Props = {
  className?: string;
};

const Footer = async ({ className }: Props) => {
  const t = await getTranslations("Footer");

  const phone = "(+244) 933 363 987";
  const phoneLink = "+244933363987";
  const instagramHandle = "/kurguen_apparel";
  const email = "<EMAIL>";
  const website = "kurguen.com";
  const contacts = [
    {
      icon: Phone,
      label: phone,
      href: `tel:${phoneLink}`,
    },
    {
      icon: Instagram,
      label: instagramHandle,
      href: `https://instagram.com${instagramHandle}`,
    },
    {
      icon: Mail,
      label: email,
      href: `mailto:${email}`,
    },
    {
      icon: Globe,
      label: website,
      href: `https://${website}`,
    },
  ];

  return (
    <div className={cn("bg-secondary overflow-x-clip", className)}>
      <Link
        href="#"
        className={cn(
          buttonVariants({ variant: "default" }),
          "text-accent hocus:bg-primary w-full rounded-none",
        )}
      >
        {t("backToTop")}
      </Link>
      <footer>
        <section className="border-primary-foreground/30 flex w-full max-w-[90rem] items-end gap-10 border-b px-6 py-10 max-md:flex-col max-md:items-center max-sm:mx-auto sm:px-20">
          <SlideAnim direction="right">
            <article
              id="contact"
              className="flex flex-1 flex-col items-center gap-5 md:items-start"
            >
              <Text as="h3" size="3xl" className="font-bold md:mb-4">
                {t("contactTitle")}
              </Text>
              <nav className="grid grid-cols-1 gap-x-10 gap-y-5 text-white md:grid-cols-2">
                {contacts.map((contact, index) => (
                  <Link
                    key={index}
                    href={contact.href}
                    target="_blank"
                    rel="noopener noreferrer"
                    title={contact.label}
                    className={cn(
                      textVariants({ size: "sm" }),
                      "hocus:text-accent flex items-center gap-2",
                    )}
                  >
                    <contact.icon
                      className="size-5 shrink-0"
                      strokeWidth={1.5}
                    />
                    <Text as="p" size="sm" className="">
                      {contact.label}
                    </Text>
                  </Link>
                ))}
              </nav>
            </article>
          </SlideAnim>
          <SlideAnim direction="left" options={{ delay: 0.5 }}>
            <article className="flex flex-col gap-5 text-white max-md:w-full max-md:items-center">
              <Text as="h6" size="base">
                {t("newsletterTitle")}
              </Text>
              <form className="flex gap-2 max-md:w-full max-md:justify-center">
                <Input
                  type="email"
                  placeholder={t("newsletterPlaceholder")}
                  className="bg-muted-foreground placeholder:text-muted w-full max-w-96 rounded-full border-none text-white sm:w-[25vw]"
                />
                <Button size="sm">{t("subscribeButton")}</Button>
              </form>
            </article>
          </SlideAnim>
        </section>

        <SlideAnim direction="up" options={{ delay: 0.5 }} stagger>
          <section className="mx-auto w-full max-w-[90rem] px-6 py-10 text-center sm:px-20">
            <KurguenIcon className="text-primary mx-auto mb-6 h-16 w-auto sm:h-28" />
            <Text
              as="p"
              size="xs"
              className="font-sans-2 font-light text-white"
            >
              {t("copyright")}
            </Text>
          </section>
        </SlideAnim>
      </footer>
    </div>
  );
};

export default Footer;
