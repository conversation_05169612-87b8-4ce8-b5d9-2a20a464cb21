import { clsx, type ClassValue } from "clsx";
import { twMerge } from "tailwind-merge";

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

// export function pick(object: { [key: string]: any }, keys: string[]) {
//   return keys.reduce(
//     (obj, key) => {
//       if (object && object.hasOwnProperty(key)) {
//         obj[key] = object[key];
//       }
//       return obj;
//     },
//     {} as { [key: string]: any },
//   );
// }
