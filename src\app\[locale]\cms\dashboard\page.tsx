import OrderCard from "@/app/[locale]/(order)/OrderCard";
import { getAllOrders } from "@/app/[locale]/(order)/actions";
import Text from "@/components/Text";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { cn } from "@/lib/utils";
import { OrderStatus } from "@/generated/prisma";
import { Package } from "lucide-react";
import { getTranslations } from "next-intl/server";

export default async function CMSDashboardPage() {
  const orders = await getAllOrders();
  const t = await getTranslations("CMS.dashboard");

  const stats = (() => {
    const pending = orders.filter(
      (order) => order.status === OrderStatus.PENDING,
    ).length;
    const completed = orders.filter(
      (order) => order.status === OrderStatus.COMPLETED,
    ).length;
    const cancelled = orders.filter(
      (order) => order.status === OrderStatus.CANCELLED,
    ).length;
    const total = orders.length;

    return { pending, completed, cancelled, total };
  })();

  const statsInfos = [
    {
      title: t("stats.total"),
      value: stats.total,
      className: "text-secondary",
    },
    {
      title: t("stats.pending"),
      value: stats.pending,
      className: "text-yellow-600",
    },
    {
      title: t("stats.completed"),
      value: stats.completed,
      className: "text-green-600",
    },
    {
      title: t("stats.cancelled"),
      value: stats.cancelled,
      className: "text-red-600",
    },
  ];

  return (
    <main className="container-full flex min-h-screen flex-col gap-8">
      {/* Stats Cards */}
      <Card className="flex flex-col gap-6 p-6">
        <header className="">
          <Text as="h1" size="lg" className="mb-2 font-bold">
            {t("title")}
          </Text>
          <Text as="p" size="sm" className="text-muted-foreground">
            {t("subtitle")}
          </Text>
        </header>
        <section className="grid grid-cols-1 gap-4 md:grid-cols-4">
          {statsInfos.map((info) => (
            <Card
              key={info.title}
              className={cn(
                info.className,
                "flex flex-row flex-wrap items-center justify-center gap-4 p-6",
              )}
            >
              <Text size="lg" className="font-bold text-current">
                {info.value}
              </Text>
              <Text size="base" className="font-bold text-current">
                {info.title}
              </Text>
            </Card>
          ))}
        </section>
      </Card>

      {/* Orders List */}
      <Card>
        <CardHeader>
          <CardTitle className="text-xl">{t("allOrders")}</CardTitle>
        </CardHeader>
        <CardContent>
          {orders.length === 0 ? (
            <div className="flex flex-col items-center justify-center py-12">
              <Package className="text-muted-foreground mb-4 h-12 w-12" />
              <Text as="h3" size="lg" className="mb-2 font-semibold">
                {t("noOrders")}
              </Text>
              <Text
                as="p"
                size="sm"
                className="text-muted-foreground text-center"
              >
                {t("noOrdersDescription")}
              </Text>
            </div>
          ) : (
            <article className="grid grid-cols-1 gap-8 md:grid-cols-2">
              {orders.map((order) => (
                <OrderCard key={order.id} order={order} />
              ))}
            </article>
          )}
        </CardContent>
      </Card>
    </main>
  );
}
