"use client";

import { OrderType } from "@/app/[locale]/(order)/model";
import { buttonVariants } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { cn } from "@/lib/utils";
import { Eye, MoreVertical } from "lucide-react";
import { useTranslations } from "next-intl";
import { useMemo, useState } from "react";
import OrderDetailsDialog from "./OrderDetailsDialog";
import { OrderStatus } from "@/generated/prisma";
import OrderChangeStatusActions from "./OrderChangeStatusActions";

type OrderRowActionsProps = {
  order: OrderType;
};

export default function OrderUserActions({ order }: OrderRowActionsProps) {
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const tOrders = useTranslations("Orders");
  const tCMS = useTranslations("CMS");

  const tStatus = useTranslations("Orders.status");

  const [alertDialog, setAlertDialog] = useState<{
    isOpen: boolean;
    newStatus: OrderStatus | null;
    title: string;
    description: string;
  }>({
    isOpen: false,
    newStatus: null,
    title: "",
    description: "",
  });

  const handleViewDetails = () => {
    setIsDialogOpen(true);
  };

  const handleCloseDialog = () => {
    setIsDialogOpen(false);
  };

  const handleStatusChange = (newStatus: OrderStatus) => {
    const statusText = tOrders(`status.${newStatus}`);
    setAlertDialog({
      isOpen: true,
      newStatus,
      title: tCMS("actions.confirmTitle", { status: statusText }),
      description: tCMS("actions.confirmDescription", {
        orderId: order.id,
        status: statusText,
      }),
    });
  };

  const availableOptions = useMemo(() => {
    const options = [];
    if (order.status === OrderStatus.PENDING) {
      options.push(
        { value: OrderStatus.COMPLETED, label: tStatus("COMPLETED") },
        { value: OrderStatus.CANCELLED, label: tStatus("CANCELLED") },
      );
    }
    return options;
  }, [order.status]);

  return (
    <>
      <DropdownMenu>
        <DropdownMenuTrigger
          className={cn(
            buttonVariants({ variant: "ghost", size: "sm" }),
            "h-8 w-8 p-0",
          )}
        >
          <MoreVertical className="h-4 w-4" />
          <span className="sr-only">{tOrders("openMenu")}</span>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end">
          <DropdownMenuItem onClick={handleViewDetails}>
            <Eye className="mr-2 h-4 w-4" />
            {tOrders("viewDetails")}
          </DropdownMenuItem>
          {availableOptions.map((option) => (
            <DropdownMenuItem
              key={option.value}
              onClick={() => handleStatusChange(option.value)}
            >
              {tCMS("actions.markAs")} {option.label}
            </DropdownMenuItem>
          ))}
        </DropdownMenuContent>
      </DropdownMenu>

      <OrderDetailsDialog
        order={order}
        isOpen={isDialogOpen}
        onClose={handleCloseDialog}
      />
      <OrderChangeStatusActions
        orderId={order.id}
        description={alertDialog.description}
        isOpen={alertDialog.isOpen}
        newStatus={alertDialog.newStatus}
        onClose={() =>
          setAlertDialog((prev) => ({
            ...prev,
            isOpen: false,
          }))
        }
        title={alertDialog.title}
      />
    </>
  );
}
