import Link from "next/link";
import { buttonVariants } from "@/components/ui/button";
import { getTranslations } from "next-intl/server";
import { cn } from "@/lib/utils";

export default async function NotFound() {
  const t = await getTranslations("NotFound");

  return (
    <div className="bg-background flex min-h-[80vh] flex-col items-center justify-center p-4 text-center">
      <div className="grid gap-4">
        <h1 className="text-foreground text-8xl font-bold">404</h1>
        <h2 className="text-white text-3xl font-semibold">{t("title")}</h2>
        <p className="text-foreground font-semibold">{t("description")}</p>
        <div className="pt-4">
          <Link href="/" className={cn(buttonVariants({ variant: "default" }))}>{t("backToHome")}</Link>
        </div>
      </div>
    </div>
  );
}
