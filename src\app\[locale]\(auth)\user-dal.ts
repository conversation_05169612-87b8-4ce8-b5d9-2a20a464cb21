import "server-only";

import { linksObj } from "@/app/links";
import { auth } from "@/lib/auth/server";
import { headers } from "next/headers";
import { redirect } from "next/navigation";
import { cache } from "react";

export const requireAdminUser = cache(async () => {
  const session = await auth.api.getSession({ headers: await headers() });

  if (!session?.user.id || session.user.role !== "admin") {
    return redirect(linksObj.signOn.href);
  }

  return session.user;
});

export const requireUser = cache(async () => {
  const session = await auth.api.getSession({ headers: await headers() });

  if (!session?.user.id) {
    return redirect(linksObj.signOn.href);
  }

  return session.user;
});

export const getUserIfExists = cache(async () => {
  const session = await auth.api.getSession({ headers: await headers() });

  if (!session?.user.id) return null;

  return session.user;
});
