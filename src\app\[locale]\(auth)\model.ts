import { z } from "zod";

export const phoneNumberSchema = z
  .string()
  .refine(
    (value) => !value || /^\+\d{3}\d{9}$/.test(value),
    "Número de telefone inválido",
  );

export const profileSchema = z.object({
  firstName: z
    .string()
    .min(2, { message: "Nome deve ter pelo menos 2 caracteres." })
    .max(30, { message: "Nome deve ter no máximo 30 caracteres." })
    .refine((value) => !/\s/.test(value), "Espaços não são permitidos"),
  lastName: z
    .string()
    .min(2, { message: "Sobrenome deve ter pelo menos 2 caracteres." })
    .max(30, { message: "Sobrenome deve ter no máximo 30 caracteres." })
    .refine((value) => !/\s/.test(value), "Espaços não são permitidos"),
  phoneNumber: phoneNumberSchema.optional().nullable(),
});

export const signOnSchema = z.object({
  email: z.string().email({ message: "Endereço de email inválido" }),
});

export const otpSchema = signOnSchema.extend({
  otp: z.string().min(1, "OTP é obrigatório").max(6, "OTP inválido"),
});

export type ProfileFormType = z.infer<typeof profileSchema>;
export type SignOnFormType = z.infer<typeof signOnSchema>;
export type OtpFormType = z.infer<typeof otpSchema>;
