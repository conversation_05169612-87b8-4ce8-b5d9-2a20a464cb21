import { auth } from "@/lib/auth/server";
import { headers } from "next/headers";
import { redirect } from "next/navigation";
import { linksObj } from "@/app/links";
import SignOnForm from "@/app/[locale]/(auth)/SignOnForm";
import AuthCard from "@/app/[locale]/(auth)/AuthCard";

const SignOnPage = async () => {
  const session = await auth.api.getSession({ headers: await headers() });

  if (session?.user.id) {
    redirect(linksObj.profile.href);
  }

  return (
    <main className="container-full flex min-h-[80vh] flex-col">
      <AuthCard mode="sign-on">
        <SignOnForm />
      </AuthCard>
    </main>
  );
};

export default SignOnPage;
