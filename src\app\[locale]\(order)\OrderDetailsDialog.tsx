"use client";

import { getPaymentDetails } from "@/app/[locale]/(cart)/paymentDetails";
import { OrderType } from "@/app/[locale]/(order)/model";
import Text from "@/components/Text";
import { Badge } from "@/components/ui/badge";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Separator } from "@/components/ui/separator";
import { formatKwanza } from "@/lib/currency";
import { cn } from "@/lib/utils";
import { Calendar, CreditCard, MapPin } from "lucide-react";
import { useTranslations } from "next-intl";
import { Fragment } from "react";

const statusColors = {
  PENDING: "bg-yellow-100 text-yellow-800",
  COMPLETED: "bg-green-100 text-green-800",
  CANCELLED: "bg-red-100 text-red-800",
} satisfies Record<OrderType["status"], string>;

type OrderDetailsDialogProps = {
  order: OrderType;
  isOpen: boolean;
  onClose: () => void;
};

export default function OrderDetailsDialog({
  order,
  isOpen,
  onClose,
}: OrderDetailsDialogProps) {
  const t = useTranslations("Orders");
  const tCheckoutForm = useTranslations("CheckoutForm");

  const paymentDetails = getPaymentDetails(tCheckoutForm, order.paymentMethod);

  const infos = [
    {
      title: t("customerInfo"),
      Icon: undefined,
      content: [
        { label: t("name"), value: `${order.firstName} ${order.lastName}` },
        { label: t("email"), value: order.email },
        { label: t("phone"), value: order.phone },
      ],
    },
    {
      title: t("storeInfo"),
      Icon: MapPin,
      content: [
        { label: tCheckoutForm("storeName"), value: order.storeName },
        { label: tCheckoutForm("storeAddress"), value: order.storeAddress },
      ],
    },
    {
      title: `${t("orderItems")} (${order.items.length})`,
      subTitle: `${t("total")}: ${formatKwanza(order.total)}`,
      Icon: undefined,
      content: order.items.map((item) => ({
        label: item.name,
        value: `${t("size")}: ${item.size.euSize} • ${t("quantity")}: ${item.quantity} • ${t("price")}: ${formatKwanza(item.price * item.quantity)}`,
      })),
    },
    {
      title: t("paymentInfo"),
      subTitle: tCheckoutForm(`paymentMethods.${order.paymentMethod}`),
      Icon: CreditCard,
      content: paymentDetails.map((detail) => ({
        label: detail.label,
        value: detail.value,
      })),
    },
  ];

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-h-[90vh] max-w-2xl overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="text-xl">
            {t("orderDetails")} - {order.id}
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-6">
          {/* Order Status and Basic Info */}
          <div className="flex flex-wrap items-center justify-between gap-4">
            <Badge
              className={cn(
                "text-xs font-semibold",
                statusColors[order.status],
              )}
            >
              {t(`status.${order.status}`)}
            </Badge>
            <div className="text-muted-foreground flex flex-wrap items-center gap-4 text-sm">
              <div className="flex items-center gap-1">
                <Calendar className="size-4" />
                {new Date(order.createdAt).toLocaleDateString()}
              </div>
            </div>
          </div>
          <Separator />

          {/* Customer Information */}
          {infos.map((info, index) => (
            <Fragment key={index}>
              <article className="flex flex-col gap-4">
                <span className="flex items-center gap-2">
                  {info.Icon && <info.Icon className="size-4" />}
                  <Text as="h5" className="font-semibold">
                    {info.title}
                  </Text>
                </span>
                {info.subTitle && (
                  <Text
                    as="p"
                    size="sm"
                    className="text-muted-foreground font-sans-2 -mt-2 flex-1 font-medium"
                  >
                    {info.subTitle}
                  </Text>
                )}
                <div className="flex flex-wrap gap-x-8 gap-y-4">
                  {info.content.map((item, index) => (
                    <span
                      key={index}
                      className="text-secondary-foreground font-sans-2 flex flex-col font-light"
                    >
                      {item.value ? (
                        <>
                          <p className="text-muted-foreground font-normal">
                            {item.label}
                          </p>
                          {item.value}
                        </>
                      ) : (
                        item.label
                      )}
                    </span>
                  ))}
                </div>
              </article>
              {index < infos.length - 1 && <Separator />}
            </Fragment>
          ))}
        </div>
      </DialogContent>
    </Dialog>
  );
}
