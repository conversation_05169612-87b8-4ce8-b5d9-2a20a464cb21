"use client";

import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { useTranslations } from "next-intl";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Loader2 } from "lucide-react";
import { SignOnFormType, signOnSchema } from "./model";
import { useState, useTransition } from "react";
import { toast } from "sonner";
import { authClient } from "@/lib/auth/client";
import OtpForm from "./OtpForm";

const SignOnForm = () => {
  const t = useTranslations("Auth");
  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useForm<SignOnFormType>({
    resolver: zodResolver(signOnSchema),
  });

  const [isPending, startTransition] = useTransition();
  const [formDataForOtp, setFormDataForOtp] = useState<SignOnFormType | null>(
    null,
  );

  const handleVerifyOtp = (data: SignOnFormType) => {
    startTransition(async () => {
      const { data: result, error } =
        await authClient.emailOtp.sendVerificationOtp({
          email: data.email,
          type: "sign-in",
        });
      if (error) {
        console.error("Send verification OTP error:", error);
        toast.error(t("unexpectedError"));
        return;
      }
      if (result?.success) {
        toast.success(t("otpSentSuccess"));
        setFormDataForOtp(data);
      } else {
        toast.error(t("unexpectedError"));
      }
    });
  };

  if (formDataForOtp) {
    return (
      <OtpForm
        formDataForOtp={formDataForOtp}
        onBack={() => setFormDataForOtp(null)}
      />
    );
  }

  return (
    <form onSubmit={handleSubmit(handleVerifyOtp)}>
      <div className="grid gap-6">
        <div className="grid gap-2">
          <Label htmlFor="email">{t("email")}</Label>
          <Input
            id="email"
            type="email"
            placeholder={t("emailPlaceholder")}
            {...register("email")}
            aria-invalid={!!errors.email}
          />
          {errors.email && (
            <p className="text-destructive text-sm">{errors.email.message}</p>
          )}
        </div>
        <Button type="submit" size="sm" className="w-full" disabled={isPending}>
          {t("signIn")}
          {isPending && <Loader2 className="size-4 animate-spin p-0.5" />}
        </Button>
      </div>
    </form>
  );
};

export default SignOnForm;
