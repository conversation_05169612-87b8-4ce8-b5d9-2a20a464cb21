import Image from "next/image";
import { cn } from "@/lib/utils";
import Text from "@/components/Text";
import sneakerSideLeftRotate from "@/assets/imgs/sneaker-side-left-rotate.png";
import sneakerSideLeftRight from "@/assets/imgs/sneaker-side-left-right.png";
import { getTranslations } from "next-intl/server";
import { SlideAnim } from "../../animations/Animations";
import { ShopInfoType, getShopsData } from "../data";
import { Link } from "@/i18n/navigation";
import { buttonVariants } from "@/components/ui/button";

type Props = {
  className?: string;
};

const StoreCard = ({ store }: { store: ShopInfoType }) => (
  <li className="border-secondary-foreground flex w-full max-w-md shrink flex-col justify-between gap-5 rounded-2xl border-2 p-10 lowercase">
    <store.logo
      className={cn(
        "h-auto max-h-8 w-full self-center fill-current sm:w-[80%]",
        store.logoClassName,
      )}
    />
    <span className="flex flex-col gap-1">
      <Text as="h6" size="base" className="font-semibold">
        {store.name}
      </Text>
      <Text as="p" size="sm">
        {store.address}
      </Text>
    </span>
    <Link
      href={`tel:+244${store.phone.replace(/\D/g, "")}`}
      className={cn(
        buttonVariants({ variant: "link", size: "sm" }),
        "hocus:text-accent w-fit normal-case",
      )}
    >
      T. {store.phone}
    </Link>
  </li>
);

const ShopsSection = async ({ className }: Props) => {
  const t = await getTranslations("ShopsSection");
  const shops = getShopsData(t);

  return (
    <div className={cn("bg-primary-foreground w-full")}>
      <section className={cn("flex flex-col gap-20", className)}>
        <SlideAnim
          direction="up"
          stagger
          selector="img"
          options={{ delay: 0.5 }}
        >
          <article className="flex w-full flex-col gap-5 sm:w-[40%]">
            <Image
              src={sneakerSideLeftRotate}
              alt="Sneaker rotated left"
              width={sneakerSideLeftRotate.width}
              height={sneakerSideLeftRotate.height}
              placeholder="blur"
              className="h-auto w-full object-contain"
            />
            <Image
              src={sneakerSideLeftRight}
              alt="Sneaker pair"
              width={sneakerSideLeftRight.width}
              height={sneakerSideLeftRight.height}
              placeholder="blur"
              className="h-auto w-full object-contain"
            />
          </article>
        </SlideAnim>
        <SlideAnim
          direction="left"
          stagger
          selector="h3,ul>li"
          options={{ delay: 0.5 }}
        >
          <article
            id="stores"
            className="flex w-full flex-col items-center gap-10 overflow-x-clip"
          >
            <Text as="h3" size="4xl" className="self-end text-white">
              {t("title")}
            </Text>
            <ul className="grid w-full grid-cols-1 justify-between justify-items-center gap-8 sm:grid-cols-2 lg:grid-cols-4">
              {shops.slice(0, 4).map((store, index) => (
                <StoreCard key={index} store={store} />
              ))}
            </ul>
          </article>
        </SlideAnim>
      </section>
    </div>
  );
};

export default ShopsSection;
