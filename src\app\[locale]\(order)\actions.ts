import "server-only";

import prisma from "@/lib/prisma";
import { orderSchema, OrderType } from "./model";
import { z } from "zod";
import { unstable_cache as cache } from "next/cache";
import { requireAdminUser, requireUser } from "../(auth)/user-dal";

export const ORDER_CACHE_KEY = "orders";

const REVALIDATE_TIME = 60; // Revalidate every 60 seconds

export const getUserOrdersById = async () => {
  const user = await requireUser();
  return cache(
    async (): Promise<OrderType[]> => {
      try {
        const orders = await prisma.order.findMany({
          where: {
            userId: user.id,
          },
          orderBy: {
            createdAt: "desc",
          },
        });

        return z.array(orderSchema).parse(orders);
      } catch (error) {
        console.error("Error fetching user orders:", error);
        return [];
      }
    },
    [user.id],
    {
      tags: [ORDER_CACHE_KEY],
      revalidate: REVALIDATE_TIME,
    },
  )();
};

export const getAllOrders = async () => {
  await requireAdminUser();
  return cache(
    async (): Promise<OrderType[]> => {
      try {
        const orders = await prisma.order.findMany({
          orderBy: {
            createdAt: "desc",
          },
        });

        return z.array(orderSchema).parse(orders);
      } catch (error) {
        console.error("Error fetching all orders:", error);
        return [];
      }
    },
    [ORDER_CACHE_KEY],
    {
      tags: [ORDER_CACHE_KEY],
      revalidate: REVALIDATE_TIME,
    },
  )();
};

export const getOrderById = async (orderId: string) => {
  await requireAdminUser();
  return cache(
    async (): Promise<OrderType | null> => {
      try {
        const order = await prisma.order.findUnique({
          where: {
            id: orderId,
          },
        });

        return orderSchema.parse(order);
      } catch (error) {
        console.error("Error fetching order:", error);
        return null;
      }
    },
    [orderId],
    {
      tags: [ORDER_CACHE_KEY],
      revalidate: REVALIDATE_TIME,
    },
  )();
};
