import React from "react";
import { cva, type VariantProps } from "class-variance-authority";
import { cn } from "@/lib/utils";

export const textVariants = cva("leading-relaxed tracking-wide", {
  variants: {
    size: {
      xs: "text-xs sm:text-sm",
      sm: "text-sm sm:text-base",
      base: "text-lg sm:text-xl",
      lg: "text-lg sm:text-xl lg:text-2xl",
      xl: "text-xl sm:text-3xl",
      "2xl": "text-xl sm:text-2xl lg:text-4xl",
      "3xl": "text-4xl sm:text-5xl",
      "4xl": "text-4xl sm:text-6xl",
      "5xl": "text-5xl sm:text-7xl",
      "6xl": "text-5xl sm:text-6xl lg:text-8xl",
    },
  },
  defaultVariants: {
    size: "base",
  },
});

type TextProps = {
  as?: "p" | "span" | "h1" | "h2" | "h3" | "h4" | "h5" | "h6" | "b";
  children: React.ReactNode;
} & VariantProps<typeof textVariants> &
  React.HTMLAttributes<HTMLElement>;

const Text = ({
  as: Component = "p",
  size,
  className,
  children,
  ...props
}: TextProps) => {
  return (
    <Component className={cn(textVariants({ size }), className)} {...props}>
      {children}
    </Component>
  );
};

export default Text;
