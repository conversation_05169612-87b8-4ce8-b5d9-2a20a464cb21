import { google } from "googleapis";
import { NextResponse } from "next/server";

const sheets = google.sheets({ version: "v4" });

export const revalidate = 60;

export async function GET() {
  try {
    const auth = new google.auth.JWT({
      email: process.env.GOOGLE_SERVICE_ACCOUNT_EMAIL,
      key: process.env.GOOGLE_PRIVATE_KEY?.replace(/\\n/g, "\n"),
      scopes: ["https://www.googleapis.com/auth/spreadsheets.readonly"],
    });

    await auth.authorize();

    const response = await sheets.spreadsheets.values.get({
      spreadsheetId: process.env.GOOGLE_SPREADSHEET_ID,
      range: "inventory!A:H",
      auth: auth,
      valueRenderOption: "UNFORMATTED_VALUE",
    });

    if (!response.data.values) {
      return NextResponse.json({ error: "No data found" }, { status: 404 });
    }

    const [headers, ...dataRows] = response.data.values;

    const products = transformDataToObjects({ headers, dataRows });

    return NextResponse.json({ data: products });
  } catch (error) {
    console.error("Error fetching data:", error);
    return NextResponse.json(
      { error: "Failed to fetch data" },
      { status: 500 },
    );
  }
}

/* eslint-disable @typescript-eslint/no-explicit-any */
function transformDataToObjects(props: {
  headers: any[];
  dataRows: any[][];
}): Record<string, any>[] {
  const { headers, dataRows } = props;
  return dataRows.map((row) => {
    const product: Record<string, any> = {};
    headers.forEach((header, index) => {
      if (typeof header === "string") {
        product[header] = row[index];
      }
    });
    return product;
  });
}
/* eslint-enable @typescript-eslint/no-explicit-any */
