import { z } from "zod";
import { submitOrderSchema } from "../(cart)/model";
import { OrderStatus } from "@/generated/prisma";

export const orderSchema = submitOrderSchema.extend({
  id: z.string(),
  userId: z.string().nullable().optional(),
  createdAt: z.date(),
  updatedAt: z.date(),
  status: z.nativeEnum(OrderStatus),
  total: z.number(),
});

export type OrderType = z.infer<typeof orderSchema>;
