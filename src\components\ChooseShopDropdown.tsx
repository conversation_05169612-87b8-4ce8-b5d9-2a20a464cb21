"use client";

import { useTranslations } from "next-intl";
import { ShopInfoType, getShopsData } from "@/app/[locale]/data";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import Text from "@/components/Text";
import { useCartStore } from "@/app/[locale]/(cart)/cartStore";
import { useEffect } from "react";

type Props = {
  defaultShop?: ShopInfoType | null;
  onStoreSelect?: (store: ShopInfoType) => void;
};

const ChooseShopDropdown = ({ onStoreSelect, defaultShop }: Props) => {
  const t = useTranslations("ShopsSection");
  const tCart = useTranslations("Cart");

  const selectedStore = useCartStore((state) => state.selectedShop);
  const setSelectedStore = useCartStore((state) => state.setSelectedShop);

  const shops = getShopsData(t);

  const handleValueChange = (value: string) => {
    const selectedStore = shops.find((store) => store.name === value);
    if (!selectedStore) return;
    setSelectedStore(selectedStore);
    onStoreSelect?.(selectedStore);
  };

  useEffect(() => {
    if (!defaultShop || selectedStore) return;
    setSelectedStore(defaultShop);
  }, [defaultShop]);

  return (
    <Select onValueChange={handleValueChange} value={selectedStore?.name}>
      <SelectTrigger className="w-fit rounded-full" size="sm">
        <SelectValue placeholder={tCart("selectStorePlaceholder")}>
          {selectedStore?.name ? (
            <Text
              as="p"
              size="xs"
              className="text-secondary-foreground font-semibold"
            >
              {selectedStore?.name}
            </Text>
          ) : (
            tCart("selectStorePlaceholder")
          )}
        </SelectValue>
      </SelectTrigger>
      <SelectContent className="text-secondary-foreground" align="center">
        {shops.map((store) => (
          <SelectItem
            key={store.name}
            value={store.name}
            disabled={store.disabled}
          >
            <span className="flex flex-col">
              <Text as="p" size="sm" className="font-semibold">
                {store.name}
              </Text>
              <Text as="p" size="xs">
                {store.address}
              </Text>
            </span>
          </SelectItem>
        ))}
      </SelectContent>
    </Select>
  );
};

export default ChooseShopDropdown;
