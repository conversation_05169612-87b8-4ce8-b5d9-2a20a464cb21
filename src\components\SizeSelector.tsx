"use client";

import { ChevronDown } from "lucide-react";
import { useTranslations } from "next-intl";
import { But<PERSON> } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { cn } from "@/lib/utils";
import Text from "./Text";
import { useSizeStore } from "@/app/[locale]/(cart)/sizeStore";
import { useState, ReactNode } from "react";

export type SizeInfo = {
  euSize: string;
  stock: number;
};

export const toUkSize = (euSize: string) => {
  const euToUkMapping: Record<string, string> = {
    "35": "2.5",
    "36": "3.5",
    "37": "4.5",
    "38": "5",
    "39": "6",
    "40": "6.5",
    "41": "7",
    "42": "8",
    "43": "9",
    "44": "10",
    "45": "11",
    "46": "12",
  };

  if (euToUkMapping[euSize]) {
    return euToUkMapping[euSize];
  }

  return "N/A";
};

type Props = {
  sizes: SizeInfo[];
  children?: ReactNode;
  onSelectedSize?: (size: SizeInfo) => void;
  className?: string;
};

export function SizeSelector({
  sizes,
  children,
  onSelectedSize,
  className,
}: Props) {
  const t = useTranslations("ProductDetails");

  const selectedSize = useSizeStore((state) => state.selectedSize);
  const setSelectedSize = useSizeStore((state) => state.setSelectedSize);

  const [isOpen, setIsOpen] = useState(false);

  const handleSelect = (sizeInfo: SizeInfo) => {
    if (sizeInfo.stock > 0) {
      if (onSelectedSize) {
        onSelectedSize(sizeInfo);
      } else {
        setSelectedSize(sizeInfo);
      }
      setIsOpen(false);
    }
  };

  const displayValue = selectedSize
    ? `${t("sizeLabelPrefix")}: ${selectedSize.euSize}`
    : t("sizePlaceholder");

  return (
    <DropdownMenu open={isOpen} onOpenChange={setIsOpen} modal={true}>
      <DropdownMenuTrigger asChild>
        {children ? (
          children
        ) : (
          <Button
            variant="outline"
            className={cn(
              "group w-full justify-between rounded-md font-medium",
              className,
            )}
          >
            {displayValue}
            <ChevronDown
              className={cn(
                "ml-2 h-4 w-4 shrink-0 transition-transform duration-200 group-data-[state=open]:rotate-180",
              )}
            />
          </Button>
        )}
      </DropdownMenuTrigger>
      <DropdownMenuContent
        onCloseAutoFocus={(event) => {
          event.preventDefault();
          document.body.style.pointerEvents = "";
        }}
        align="start"
        className="w-[--radix-dropdown-menu-trigger-width]"
      >
        <div className="text-foreground grid grid-cols-3 items-center gap-x-2.5 px-2 py-1.5 font-semibold">
          <Text size="sm">{t("euSize")}</Text>
          <Text size="sm">{t("ukSize")}</Text>
          <Text size="sm" className="text-right">
            {t("stock")}
          </Text>
        </div>
        {sizes.map((sizeInfo) => {
          const isSoldOut = sizeInfo.stock === 0;
          return (
            <DropdownMenuItem
              key={sizeInfo.euSize}
              className={cn(
                "grid cursor-pointer grid-cols-3 items-center gap-x-2.5 font-medium",
                isSoldOut && "cursor-not-allowed opacity-30",
              )}
              onSelect={() => handleSelect(sizeInfo)}
              disabled={isSoldOut}
            >
              <Text size="xs">{sizeInfo.euSize}</Text>
              <Text size="xs">{toUkSize(sizeInfo.euSize)}</Text>
              <Text
                size="xs"
                className={cn(
                  "text-right",
                  sizeInfo.stock === 0
                    ? "text-destructive"
                    : sizeInfo.stock > 0 && sizeInfo.stock <= 10
                      ? "text-primary"
                      : "text-foreground",
                )}
              >
                {t("stockStatus", { count: sizeInfo.stock })}
              </Text>
            </DropdownMenuItem>
          );
        })}
      </DropdownMenuContent>
    </DropdownMenu>
  );
}
