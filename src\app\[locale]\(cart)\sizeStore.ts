import { create } from "zustand";
import { persist } from "zustand/middleware";
import { SizeInfo } from "@/components/SizeSelector";

type SizeStore = {
  selectedSize: SizeInfo | null;
  setSelectedSize: (size: SizeInfo | null) => void;
};

export const useSizeStore = create<SizeStore>()(
  persist(
    (set) => ({
      selectedSize: null,
      setSelectedSize: (size) => set({ selectedSize: size }),
    }),
    {
      name: "size-storage",
    },
  ),
);
