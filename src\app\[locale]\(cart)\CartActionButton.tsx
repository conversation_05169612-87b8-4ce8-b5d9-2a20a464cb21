"use client";

import { Button } from "@/components/ui/button";
import { useCartStore } from "@/app/[locale]/(cart)/cartStore";
import { useSizeStore } from "@/app/[locale]/(cart)/sizeStore";
import { toast } from "sonner";
import { useTranslations } from "next-intl";
import sneakerSideLeft from "@/assets/imgs/sneaker-side-left.png";
import { cn } from "@/lib/utils";
import { CartDialog } from "@/app/[locale]/(cart)/CartDialog";

type CartActionType = "add-to-cart" | "buy-now";

type Props = {
  type: CartActionType;
  className?: string;
};

const CartActionButton = ({ type, className }: Props) => {
  const t = useTranslations("ProductDetails");
  const tCart = useTranslations("Cart");
  const { addItem } = useCartStore();
  const { selectedSize } = useSizeStore();

  const handleAction = () => {
    if (!selectedSize) {
      toast.error(t("selectSizeFirst"), {
        closeButton: true,
      });
      return;
    }

    const item = {
      id: t("productId"),
      name: t("productName"),
      price: parseInt(t("price"), 10) || 0,
      image: sneakerSideLeft.src,
      size: selectedSize,
      quantity: 1,
    };

    addItem(item, {
      messages: {
        alreadyExists: tCart("sizeAlreadyExists"),
        addedToCart: t("addedToCart"),
      },
    });
  };

  const buttonContent = (
    <Button
      variant={type === "add-to-cart" ? "secondary" : "default"}
      className={cn(className)}
      onClick={handleAction}
    >
      {type === "add-to-cart" ? t("addToCart") : t("buyNow")}
    </Button>
  );

  if (type === "buy-now") {
    return <CartDialog>{buttonContent}</CartDialog>;
  }

  return buttonContent;
};

export default CartActionButton;
