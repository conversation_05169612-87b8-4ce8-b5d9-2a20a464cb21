import { getTranslations } from "next-intl/server";

import sneakersTopBottom from "@/assets/imgs/sneakers-top-bottom.png";
import sneakerSideLeftRotate from "@/assets/imgs/sneaker-side-left-rotate.png";
import sneakerSideLeft from "@/assets/imgs/sneaker-side-left.png";
import sneakerSideRightRotate from "@/assets/imgs/sneaker-side-right-rotate.png";

export const getSliders = async () => {
  const t = await getTranslations("HeroSection");
  return [
    {
      title: t("slide1Title"),
      subtitle: t("slide1Subtitle"),
      src: sneakerSideRightRotate,
      alt: "Sneaker side right rotated view",
    },
    {
      title: t("slide2Title"),
      src: sneakerSideLeft,
      alt: "Sneaker side left view",
    },
    {
      title: t("slide3Title"),
      subtitle: t("slide3Subtitle"),
      src: sneakerSideLeftRotate,
      alt: "Sneaker side left rotated view",
      withLineLarge: true,
    },
    {
      title: t("slide4Title"),
      subtitle: t("slide4Subtitle"),
      src: sneakersTopBottom,
      alt: "Sneaker top and bottom view",
    },
  ];
};

export type SliderItemType = Awaited<ReturnType<typeof getSliders>>[number];
