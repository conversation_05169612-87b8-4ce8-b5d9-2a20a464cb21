"use client";
import { animate, inView, stagger as staggerFn } from "motion";
import React, {
  cloneElement,
  ReactElement,
  useLayoutEffect,
  useRef,
} from "react";

type Props = {
  selector?: Element | Element[] | NodeListOf<Element> | string;
  direction?: "up" | "down" | "left" | "right";
  stagger?: boolean;
  options?: {
    duration?: number;
    delay?: number;
  };
};

export const SlideScaleAnim = ({
  children,
  ...props
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
}: Props & { children: ReactElement<any> }) => {
  const {
    selector,
    direction = "down",
    stagger = true,
    options = {
      duration: 0.5,
      delay: 0,
    },
  } = props || {};

  const ref = React.useRef<HTMLElement>(null);

  useLayoutEffect(() => {
    if (!ref.current) return;

    const _sel =
      typeof selector === "string"
        ? ref.current.querySelectorAll(selector)
        : selector || ref.current;

    const initialPositions = {
      down: { y: [-200, 0], transformOrigin: "top" },
      up: { y: [200, 0], transformOrigin: "bottom" },
      left: { x: [200, 0], transformOrigin: "left" },
      right: { x: [-200, 0], transformOrigin: "right" },
    };

    animate(_sel, { opacity: 0 }, { duration: 0 });

    const stop = inView(
      ref.current,
      () => {
        animate(
          _sel,
          {
            ...initialPositions[direction || "down"],
            opacity: [0, 1],
            scale: [0, 1],
          },
          {
            ...options,
            delay: stagger ? staggerFn(options.delay) : options.delay,
            ease: "circOut",
          },
        );
        return () => stop();
      },
      { amount: 0.3 },
    );

    return () => stop();
  }, [ref]);

  return cloneElement(children, { ...children.props, ref });
};

export const SlideAnim = ({
  children,
  ...props
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
}: Props & { children: ReactElement<any> }) => {
  const {
    selector,
    direction = "down",
    stagger = true,
    options = {
      duration: 0.5,
      delay: 0,
    },
  } = props || {};

  const ref = React.useRef<HTMLElement>(null);

  useLayoutEffect(() => {
    if (!ref.current) return;

    const _sel =
      typeof selector === "string"
        ? ref.current.querySelectorAll(selector)
        : selector || ref.current;

    const initialPositions = {
      down: { y: [-200, 0] },
      up: { y: [200, 0] },
      left: { x: [200, 0] },
      right: { x: [-200, 0] },
    };

    animate(_sel, { opacity: 0 }, { duration: 0 });

    const stop = inView(
      ref.current,
      () => {
        animate(
          _sel,
          {
            ...initialPositions[direction || "down"],
            opacity: [0, 1],
          },
          {
            ...options,
            delay: stagger ? staggerFn(options.delay) : options.delay,
            ease: "circOut",
          },
        );
        return () => stop();
      },
      { amount: 0.3 },
    );

    return () => stop();
  }, [ref]);

  return cloneElement(children, { ...children.props, ref });
};

export const ScaleAnim = ({
  children,
  ...props
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
}: Props & { children: ReactElement<any> }) => {
  const {
    selector,
    stagger = false,
    options = {
      duration: 0.5,
      delay: 0,
    },
  } = props || {};
  const ref = useRef<HTMLElement>(null);

  useLayoutEffect(() => {
    if (!ref.current) return;
    const _sel =
      typeof selector === "string"
        ? ref.current.querySelectorAll(selector)
        : selector || ref.current;
    animate(_sel, { opacity: 0, scale: 0 }, { duration: 0 });
    const stop = inView(
      ref.current,
      () => {
        animate(
          _sel,
          {
            opacity: [0, 1],
            scale: [0, 1],
          },
          {
            ...options,
            delay: stagger ? staggerFn(options.delay) : options.delay,
            ease: "circOut",
          },
        );
        return () => stop();
      },
      { amount: 0.4 },
    );

    return () => stop();
  }, [ref]);
  return cloneElement(children, { ...children.props, ref });
};

export const RotateScaleAnim = ({
  children,
  ...props
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
}: Props & { children: ReactElement<any> }) => {
  const {
    selector,
    stagger = false,
    options = {
      duration: 0.7,
      delay: 0,
    },
  } = props || {};
  const ref = useRef<HTMLElement>(null);

  useLayoutEffect(() => {
    if (!ref.current) return;
    const _sel =
      typeof selector === "string"
        ? ref.current.querySelectorAll(selector)
        : selector || ref.current;

    animate(_sel, { opacity: 0 }, { duration: 0 });

    const stop = inView(
      ref.current,
      () => {
        animate(
          _sel,
          {
            opacity: [0, 1],
            scale: [0, 1],
            rotate: ["-30deg", "0deg"],
          },
          {
            ...options,
            delay: stagger ? staggerFn(options.delay) : options.delay,
            ease: "backOut",
          },
        );
        return () => stop();
      },
      { amount: 0.4 },
    );
    return () => stop();
  }, [ref]);

  return cloneElement(children, { ...children.props, ref });
};

export const MaskLeftRightRevealAnim = ({
  children,
  ...props
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
}: Props & { children: ReactElement<any> }) => {
  const {
    selector,
    stagger = false,
    options = {
      duration: 0.7,
      delay: 0,
    },
  } = props || {};
  const ref = useRef<HTMLElement>(null);

  useLayoutEffect(() => {
    if (!ref.current) return;
    const _sel =
      typeof selector === "string"
        ? ref.current.querySelectorAll(selector)
        : selector || ref.current;

    animate(_sel, { opacity: 0 }, { duration: 0 });

    const stop = inView(
      ref.current,
      () => {
        animate(
          _sel,
          {
            opacity: [0, 1],
            clipPath: [
              "polygon(0 0, 0 0, 0 50%, 100% 50%, 100% 100%, 100% 100%, 100% 50%, 0 50%)",
              "polygon(0 0, 100% 0, 100% 50%, 0 50%, 0 100%, 100% 100%, 100% 50%, 0 50%)",
            ],
          },
          {
            ...options,
            delay: stagger ? staggerFn(options.delay) : options.delay,
            ease: "easeOut", // Use an appropriate easing function
          },
        );
        return () => stop();
      },
      { amount: 0.4 }, // Adjust trigger amount if needed
    );
    return () => stop();
  }, [ref]);

  return cloneElement(children, { ...children.props, ref });
};
